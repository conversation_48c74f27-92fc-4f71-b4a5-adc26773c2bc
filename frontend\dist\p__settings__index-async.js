((typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_teamauth-frontend"] = (typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_teamauth-frontend"] || []).push([
        ['p__settings__index'],
{ "src/pages/settings/TeamManagementCard.tsx": function (module, exports, __mako_require__){
"use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _max = __mako_require__("src/.umi/exports.ts");
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _services = __mako_require__("src/services/index.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { Title, Text } = _antd.Typography;
const { TextArea } = _antd.Input;
/**
 * 团队管理设置卡片组件
 *
 * 提供团队相关的管理功能，包括：
 * - 创建新团队
 * - 查看用户的团队列表
 * - 团队快速操作
 * - 团队设置入口
 *
 * 功能特点：
 * - 创建团队模态框
 * - 团队列表展示
 * - 快速导航到团队管理
 * - 响应式设计
 */ const TeamManagementCard = ()=>{
    _s();
    const [form] = _antd.Form.useForm();
    const [editForm] = _antd.Form.useForm();
    const [createModalVisible, setCreateModalVisible] = (0, _react.useState)(false);
    const [createLoading, setCreateLoading] = (0, _react.useState)(false);
    const [teams, setTeams] = (0, _react.useState)([]);
    const [teamsLoading, setTeamsLoading] = (0, _react.useState)(false);
    const [editModalVisible, setEditModalVisible] = (0, _react.useState)(false);
    const [editingTeam, setEditingTeam] = (0, _react.useState)(null);
    const [editLoading, setEditLoading] = (0, _react.useState)(false);
    const [operationLoading, setOperationLoading] = (0, _react.useState)({});
    const { initialState, setInitialState } = (0, _max.useModel)('@@initialState');
    /**
   * 获取用户团队列表
   *
   * 在组件挂载时获取用户的团队列表
   */ (0, _react.useEffect)(()=>{
        if (initialState === null || initialState === void 0 ? void 0 : initialState.currentUser) fetchTeams();
    }, [
        initialState === null || initialState === void 0 ? void 0 : initialState.currentUser
    ]);
    /**
   * 获取团队列表数据
   */ const fetchTeams = async ()=>{
        setTeamsLoading(true);
        try {
            const teamsData = await _services.TeamService.getUserTeamsWithStats();
            setTeams(teamsData);
        } catch (error) {
            console.error('获取团队列表失败:', error);
        } finally{
            setTeamsLoading(false);
        }
    };
    /**
   * 创建团队处理函数
   *
   * 处理团队创建的完整流程：
   * 1. 表单验证
   * 2. API调用
   * 3. 更新团队列表
   * 4. 关闭模态框
   * 5. 用户反馈
   */ const handleCreateTeam = async (values)=>{
        setCreateLoading(true);
        try {
            await _services.TeamService.createTeam(values);
            // 重新获取团队列表
            await fetchTeams();
            // 关闭模态框并重置表单
            setCreateModalVisible(false);
            form.resetFields();
            _antd.message.success('团队创建成功！');
        } catch (error) {
            // 错误处理由响应拦截器统一处理
            console.error('创建团队失败:', error);
        } finally{
            setCreateLoading(false);
        }
    };
    /**
   * 编辑团队名称
   */ const handleEditTeam = (team)=>{
        setEditingTeam(team);
        editForm.setFieldsValue({
            name: team.name,
            description: team.description
        });
        setEditModalVisible(true);
    };
    /**
   * 保存团队编辑
   */ const handleSaveTeamEdit = async (values)=>{
        if (!editingTeam) return;
        setEditLoading(true);
        try {
            // 先切换到目标团队
            await _services.AuthService.selectTeam({
                teamId: editingTeam.id
            });
            // 更新团队信息
            await _services.TeamService.updateCurrentTeam({
                name: values.name,
                description: values.description
            });
            // 重新获取团队列表
            await fetchTeams();
            // 关闭模态框
            setEditModalVisible(false);
            setEditingTeam(null);
            editForm.resetFields();
            _antd.message.success('团队信息更新成功！');
        } catch (error) {
            console.error('更新团队失败:', error);
            _antd.message.error('更新团队失败');
        } finally{
            setEditLoading(false);
        }
    };
    /**
   * 删除团队
   */ const handleDeleteTeam = async (team)=>{
        setOperationLoading((prev)=>({
                ...prev,
                [team.id]: true
            }));
        try {
            await _services.TeamService.deleteTeam(team.id);
            // 重新获取团队列表
            await fetchTeams();
            _antd.message.success('团队删除成功！');
        } catch (error) {
            console.error('删除团队失败:', error);
            _antd.message.error('删除团队失败');
        } finally{
            setOperationLoading((prev)=>({
                    ...prev,
                    [team.id]: false
                }));
        }
    };
    /**
   * 退出团队
   */ const handleLeaveTeam = async (team)=>{
        setOperationLoading((prev)=>({
                ...prev,
                [team.id]: true
            }));
        try {
            await _services.TeamService.leaveTeam();
            // 重新获取团队列表
            await fetchTeams();
            // 更新全局状态，清除当前团队
            if (setInitialState) await setInitialState((prevState)=>({
                    ...prevState,
                    currentTeam: undefined
                }));
            _antd.message.success('已退出团队！');
        } catch (error) {
            console.error('退出团队失败:', error);
            _antd.message.error('退出团队失败');
        } finally{
            setOperationLoading((prev)=>({
                    ...prev,
                    [team.id]: false
                }));
        }
    };
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_jsxdevruntime.Fragment, {
        children: [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                className: "dashboard-card",
                style: {
                    borderRadius: 16,
                    boxShadow: '0 6px 20px rgba(0,0,0,0.08)',
                    border: 'none',
                    background: 'linear-gradient(145deg, #ffffff, #f8faff)',
                    height: '100%'
                },
                title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                    style: {
                        display: 'flex',
                        alignItems: 'center',
                        gap: 12
                    },
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {
                            style: {
                                fontSize: '24px',
                                color: '#1890ff',
                                padding: '8px',
                                backgroundColor: '#e6f7ff',
                                borderRadius: '8px'
                            }
                        }, void 0, false, {
                            fileName: "src/pages/settings/TeamManagementCard.tsx",
                            lineNumber: 227,
                            columnNumber: 13
                        }, void 0),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                    level: 4,
                                    style: {
                                        margin: 0,
                                        background: 'linear-gradient(135deg, #1890ff, #722ed1)',
                                        WebkitBackgroundClip: 'text',
                                        WebkitTextFillColor: 'transparent',
                                        fontWeight: 600
                                    },
                                    children: "团队管理"
                                }, void 0, false, {
                                    fileName: "src/pages/settings/TeamManagementCard.tsx",
                                    lineNumber: 237,
                                    columnNumber: 15
                                }, void 0),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                    type: "secondary",
                                    style: {
                                        fontSize: '12px'
                                    },
                                    children: "创建和管理您的团队"
                                }, void 0, false, {
                                    fileName: "src/pages/settings/TeamManagementCard.tsx",
                                    lineNumber: 249,
                                    columnNumber: 15
                                }, void 0)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/settings/TeamManagementCard.tsx",
                            lineNumber: 236,
                            columnNumber: 13
                        }, void 0)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/settings/TeamManagementCard.tsx",
                    lineNumber: 226,
                    columnNumber: 11
                }, void 0),
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                        style: {
                            marginBottom: 24
                        },
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                            type: "primary",
                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.PlusOutlined, {}, void 0, false, {
                                fileName: "src/pages/settings/TeamManagementCard.tsx",
                                lineNumber: 260,
                                columnNumber: 19
                            }, void 0),
                            onClick: ()=>setCreateModalVisible(true),
                            size: "large",
                            block: true,
                            style: {
                                borderRadius: 8,
                                background: 'linear-gradient(135deg, #1890ff, #722ed1)',
                                border: 'none',
                                boxShadow: '0 2px 8px rgba(24, 144, 255, 0.3)',
                                height: '48px',
                                fontSize: '16px',
                                fontWeight: 600
                            },
                            children: "创建新团队"
                        }, void 0, false, {
                            fileName: "src/pages/settings/TeamManagementCard.tsx",
                            lineNumber: 258,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/settings/TeamManagementCard.tsx",
                        lineNumber: 257,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Divider, {
                        style: {
                            margin: '24px 0'
                        },
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                            type: "secondary",
                            children: "我的团队"
                        }, void 0, false, {
                            fileName: "src/pages/settings/TeamManagementCard.tsx",
                            lineNumber: 279,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/settings/TeamManagementCard.tsx",
                        lineNumber: 278,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List, {
                        loading: teamsLoading,
                        dataSource: teams,
                        renderItem: (team)=>{
                            const getTeamActions = (team)=>{
                                if (team.isCreator) return [
                                    {
                                        key: 'edit',
                                        label: '编辑团队名称',
                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.EditOutlined, {}, void 0, false, {
                                            fileName: "src/pages/settings/TeamManagementCard.tsx",
                                            lineNumber: 293,
                                            columnNumber: 27
                                        }, void 0),
                                        onClick: ()=>handleEditTeam(team)
                                    },
                                    {
                                        key: 'delete',
                                        label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Popconfirm, {
                                            title: "删除团队",
                                            description: `确定要删除团队"${team.name}"吗？此操作不可恢复！`,
                                            onConfirm: ()=>handleDeleteTeam(team),
                                            okText: "确定删除",
                                            cancelText: "取消",
                                            okButtonProps: {
                                                danger: true
                                            },
                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("span", {
                                                style: {
                                                    color: '#ff4d4f'
                                                },
                                                children: "删除团队"
                                            }, void 0, false, {
                                                fileName: "src/pages/settings/TeamManagementCard.tsx",
                                                lineNumber: 307,
                                                columnNumber: 25
                                            }, void 0)
                                        }, void 0, false, {
                                            fileName: "src/pages/settings/TeamManagementCard.tsx",
                                            lineNumber: 299,
                                            columnNumber: 23
                                        }, void 0),
                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {}, void 0, false, {
                                            fileName: "src/pages/settings/TeamManagementCard.tsx",
                                            lineNumber: 310,
                                            columnNumber: 27
                                        }, void 0),
                                        danger: true
                                    }
                                ];
                                else return [
                                    {
                                        key: 'leave',
                                        label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Popconfirm, {
                                            title: "退出团队",
                                            description: `确定要退出团队"${team.name}"吗？`,
                                            onConfirm: ()=>handleLeaveTeam(team),
                                            okText: "确定退出",
                                            cancelText: "取消",
                                            okButtonProps: {
                                                danger: true
                                            },
                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("span", {
                                                style: {
                                                    color: '#ff4d4f'
                                                },
                                                children: "退出团队"
                                            }, void 0, false, {
                                                fileName: "src/pages/settings/TeamManagementCard.tsx",
                                                lineNumber: 327,
                                                columnNumber: 25
                                            }, void 0)
                                        }, void 0, false, {
                                            fileName: "src/pages/settings/TeamManagementCard.tsx",
                                            lineNumber: 319,
                                            columnNumber: 23
                                        }, void 0),
                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.LogoutOutlined, {}, void 0, false, {
                                            fileName: "src/pages/settings/TeamManagementCard.tsx",
                                            lineNumber: 330,
                                            columnNumber: 27
                                        }, void 0),
                                        danger: true
                                    }
                                ];
                            };
                            return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List.Item, {
                                style: {
                                    padding: '12px 0',
                                    borderBottom: '1px solid #f0f0f0'
                                },
                                actions: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Dropdown, {
                                        menu: {
                                            items: getTeamActions(team)
                                        },
                                        trigger: [
                                            'click'
                                        ],
                                        placement: "bottomRight",
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                            type: "text",
                                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.MoreOutlined, {}, void 0, false, {
                                                fileName: "src/pages/settings/TeamManagementCard.tsx",
                                                lineNumber: 354,
                                                columnNumber: 29
                                            }, void 0),
                                            loading: operationLoading[team.id],
                                            style: {
                                                fontSize: '16px'
                                            }
                                        }, void 0, false, {
                                            fileName: "src/pages/settings/TeamManagementCard.tsx",
                                            lineNumber: 352,
                                            columnNumber: 21
                                        }, void 0)
                                    }, "actions", false, {
                                        fileName: "src/pages/settings/TeamManagementCard.tsx",
                                        lineNumber: 344,
                                        columnNumber: 19
                                    }, void 0)
                                ],
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List.Item.Meta, {
                                    avatar: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                        style: {
                                            width: 40,
                                            height: 40,
                                            borderRadius: 8,
                                            background: 'linear-gradient(135deg, #1890ff, #722ed1)',
                                            display: 'flex',
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            color: 'white',
                                            fontWeight: 'bold',
                                            fontSize: '16px'
                                        },
                                        children: team.name.charAt(0).toUpperCase()
                                    }, void 0, false, {
                                        fileName: "src/pages/settings/TeamManagementCard.tsx",
                                        lineNumber: 363,
                                        columnNumber: 21
                                    }, void 0),
                                    title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                        style: {
                                            display: 'flex',
                                            alignItems: 'center',
                                            gap: 8
                                        },
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                strong: true,
                                                style: {
                                                    fontSize: '14px'
                                                },
                                                children: team.name
                                            }, void 0, false, {
                                                fileName: "src/pages/settings/TeamManagementCard.tsx",
                                                lineNumber: 382,
                                                columnNumber: 23
                                            }, void 0),
                                            team.isCreator && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                                                color: "blue",
                                                style: {
                                                    fontSize: '10px'
                                                },
                                                children: "创建者"
                                            }, void 0, false, {
                                                fileName: "src/pages/settings/TeamManagementCard.tsx",
                                                lineNumber: 386,
                                                columnNumber: 25
                                            }, void 0)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/settings/TeamManagementCard.tsx",
                                        lineNumber: 381,
                                        columnNumber: 21
                                    }, void 0),
                                    description: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        type: "secondary",
                                        style: {
                                            fontSize: '12px'
                                        },
                                        children: [
                                            team.memberCount,
                                            " 名成员"
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/settings/TeamManagementCard.tsx",
                                        lineNumber: 393,
                                        columnNumber: 21
                                    }, void 0)
                                }, void 0, false, {
                                    fileName: "src/pages/settings/TeamManagementCard.tsx",
                                    lineNumber: 361,
                                    columnNumber: 17
                                }, void 0)
                            }, void 0, false, {
                                fileName: "src/pages/settings/TeamManagementCard.tsx",
                                lineNumber: 338,
                                columnNumber: 15
                            }, void 0);
                        },
                        locale: {
                            emptyText: '暂无团队，创建您的第一个团队吧！'
                        }
                    }, void 0, false, {
                        fileName: "src/pages/settings/TeamManagementCard.tsx",
                        lineNumber: 283,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "src/pages/settings/TeamManagementCard.tsx",
                lineNumber: 216,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                title: "编辑团队信息",
                open: editModalVisible,
                onCancel: ()=>{
                    setEditModalVisible(false);
                    setEditingTeam(null);
                    editForm.resetFields();
                },
                footer: null,
                width: 500,
                style: {
                    top: 100
                },
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
                    form: editForm,
                    layout: "vertical",
                    onFinish: handleSaveTeamEdit,
                    autoComplete: "off",
                    style: {
                        marginTop: 24
                    },
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            label: "团队名称",
                            name: "name",
                            rules: [
                                {
                                    required: true,
                                    message: '请输入团队名称！'
                                },
                                {
                                    max: 100,
                                    message: '团队名称长度不能超过100字符！'
                                },
                                {
                                    min: 2,
                                    message: '团队名称至少需要2个字符！'
                                }
                            ],
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                placeholder: "请输入团队名称",
                                size: "large"
                            }, void 0, false, {
                                fileName: "src/pages/settings/TeamManagementCard.tsx",
                                lineNumber: 436,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/settings/TeamManagementCard.tsx",
                            lineNumber: 427,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            label: "团队描述",
                            name: "description",
                            rules: [
                                {
                                    max: 500,
                                    message: '团队描述长度不能超过500字符！'
                                }
                            ],
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(TextArea, {
                                placeholder: "请输入团队描述（可选）",
                                rows: 4,
                                showCount: true,
                                maxLength: 500
                            }, void 0, false, {
                                fileName: "src/pages/settings/TeamManagementCard.tsx",
                                lineNumber: 444,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/settings/TeamManagementCard.tsx",
                            lineNumber: 439,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            style: {
                                marginBottom: 0,
                                textAlign: 'right'
                            },
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        onClick: ()=>{
                                            setEditModalVisible(false);
                                            setEditingTeam(null);
                                            editForm.resetFields();
                                        },
                                        children: "取消"
                                    }, void 0, false, {
                                        fileName: "src/pages/settings/TeamManagementCard.tsx",
                                        lineNumber: 454,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        type: "primary",
                                        htmlType: "submit",
                                        loading: editLoading,
                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.EditOutlined, {}, void 0, false, {
                                            fileName: "src/pages/settings/TeamManagementCard.tsx",
                                            lineNumber: 467,
                                            columnNumber: 23
                                        }, void 0),
                                        children: "保存修改"
                                    }, void 0, false, {
                                        fileName: "src/pages/settings/TeamManagementCard.tsx",
                                        lineNumber: 463,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/settings/TeamManagementCard.tsx",
                                lineNumber: 453,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/settings/TeamManagementCard.tsx",
                            lineNumber: 452,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/settings/TeamManagementCard.tsx",
                    lineNumber: 420,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "src/pages/settings/TeamManagementCard.tsx",
                lineNumber: 408,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                    align: "center",
                    gap: 8,
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {
                            style: {
                                color: '#1890ff'
                            }
                        }, void 0, false, {
                            fileName: "src/pages/settings/TeamManagementCard.tsx",
                            lineNumber: 480,
                            columnNumber: 13
                        }, void 0),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("span", {
                            children: "创建新团队"
                        }, void 0, false, {
                            fileName: "src/pages/settings/TeamManagementCard.tsx",
                            lineNumber: 481,
                            columnNumber: 13
                        }, void 0)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/settings/TeamManagementCard.tsx",
                    lineNumber: 479,
                    columnNumber: 11
                }, void 0),
                open: createModalVisible,
                onCancel: ()=>{
                    setCreateModalVisible(false);
                    form.resetFields();
                },
                footer: null,
                width: 500,
                style: {
                    top: 100
                },
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
                    form: form,
                    layout: "vertical",
                    onFinish: handleCreateTeam,
                    autoComplete: "off",
                    style: {
                        marginTop: 24
                    },
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            label: "团队名称",
                            name: "name",
                            rules: [
                                {
                                    required: true,
                                    message: '请输入团队名称！'
                                },
                                {
                                    max: 100,
                                    message: '团队名称长度不能超过100字符！'
                                },
                                {
                                    min: 2,
                                    message: '团队名称至少需要2个字符！'
                                }
                            ],
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                placeholder: "请输入团队名称",
                                size: "large"
                            }, void 0, false, {
                                fileName: "src/pages/settings/TeamManagementCard.tsx",
                                lineNumber: 509,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/settings/TeamManagementCard.tsx",
                            lineNumber: 500,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            label: "团队描述",
                            name: "description",
                            rules: [
                                {
                                    max: 500,
                                    message: '团队描述长度不能超过500字符！'
                                }
                            ],
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(TextArea, {
                                placeholder: "请输入团队描述（可选）",
                                rows: 4,
                                showCount: true,
                                maxLength: 500
                            }, void 0, false, {
                                fileName: "src/pages/settings/TeamManagementCard.tsx",
                                lineNumber: 517,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/settings/TeamManagementCard.tsx",
                            lineNumber: 512,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            style: {
                                marginBottom: 0,
                                marginTop: 32
                            },
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                justify: "end",
                                gap: 12,
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        onClick: ()=>{
                                            setCreateModalVisible(false);
                                            form.resetFields();
                                        },
                                        children: "取消"
                                    }, void 0, false, {
                                        fileName: "src/pages/settings/TeamManagementCard.tsx",
                                        lineNumber: 527,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        type: "primary",
                                        htmlType: "submit",
                                        loading: createLoading,
                                        style: {
                                            background: 'linear-gradient(135deg, #1890ff, #722ed1)',
                                            border: 'none'
                                        },
                                        children: "创建团队"
                                    }, void 0, false, {
                                        fileName: "src/pages/settings/TeamManagementCard.tsx",
                                        lineNumber: 535,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/settings/TeamManagementCard.tsx",
                                lineNumber: 526,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/settings/TeamManagementCard.tsx",
                            lineNumber: 525,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/settings/TeamManagementCard.tsx",
                    lineNumber: 493,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "src/pages/settings/TeamManagementCard.tsx",
                lineNumber: 477,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true);
};
_s(TeamManagementCard, "FR0pylWGWXJv/L4NI/jcs0OXCVY=", false, function() {
    return [
        _antd.Form.useForm,
        _antd.Form.useForm,
        _max.useModel
    ];
});
_c = TeamManagementCard;
var _default = TeamManagementCard;
var _c;
$RefreshReg$(_c, "TeamManagementCard");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/pages/settings/UserProfileCard.tsx": function (module, exports, __mako_require__){
"use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _max = __mako_require__("src/.umi/exports.ts");
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _services = __mako_require__("src/services/index.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { Title, Text } = _antd.Typography;
/**
 * 用户个人设置卡片组件
 *
 * 提供用户个人信息的查看和编辑功能，包括：
 * - 用户名修改
 * - 联系电话设置
 * - 头像显示（邮箱不可修改）
 * - 个人资料保存
 *
 * 功能特点：
 * - 实时表单验证
 * - 自动保存状态反馈
 * - 响应式设计
 * - 错误处理
 */ const UserProfileCard = ()=>{
    _s();
    const [form] = _antd.Form.useForm();
    const [loading, setLoading] = (0, _react.useState)(false);
    const { initialState, setInitialState } = (0, _max.useModel)('@@initialState');
    /**
   * 初始化表单数据
   *
   * 当用户信息加载完成后，自动填充表单字段
   */ (0, _react.useEffect)(()=>{
        if (initialState === null || initialState === void 0 ? void 0 : initialState.currentUser) form.setFieldsValue({
            name: initialState.currentUser.name,
            email: initialState.currentUser.email,
            telephone: initialState.currentUser.telephone || ''
        });
    }, [
        initialState === null || initialState === void 0 ? void 0 : initialState.currentUser,
        form
    ]);
    /**
   * 保存用户个人资料
   *
   * 处理用户个人信息的更新，包括：
   * 1. 表单验证
   * 2. API调用
   * 3. 全局状态更新
   * 4. 用户反馈
   */ const handleSaveProfile = async (values)=>{
        setLoading(true);
        try {
            // 调用用户信息更新API
            const updatedUser = await _services.UserService.updateProfile(values);
            // 更新全局状态中的用户信息
            await setInitialState((prevState)=>({
                    ...prevState,
                    currentUser: updatedUser
                }));
            _antd.message.success('个人资料更新成功');
        } catch (error) {
            // 错误处理由响应拦截器统一处理
            console.error('更新个人资料失败:', error);
        } finally{
            setLoading(false);
        }
    };
    /**
   * 获取用户头像显示
   *
   * 根据用户名生成头像显示，如果没有用户名则使用邮箱前缀
   */ const getUserAvatar = ()=>{
        const user = initialState === null || initialState === void 0 ? void 0 : initialState.currentUser;
        if (!user) return 'U';
        return user.name ? user.name.charAt(0).toUpperCase() : user.email ? user.email.charAt(0).toUpperCase() : 'U';
    };
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
        className: "dashboard-card",
        style: {
            borderRadius: 16,
            boxShadow: '0 6px 20px rgba(0,0,0,0.08)',
            border: 'none',
            background: 'linear-gradient(145deg, #ffffff, #f8faff)',
            height: '100%'
        },
        title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
            style: {
                display: 'flex',
                alignItems: 'center',
                gap: 12
            },
            children: [
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Avatar, {
                    size: 40,
                    style: {
                        backgroundColor: '#1890ff',
                        fontSize: '18px',
                        fontWeight: 'bold'
                    },
                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                        fileName: "src/pages/settings/UserProfileCard.tsx",
                        lineNumber: 122,
                        columnNumber: 19
                    }, void 0),
                    children: getUserAvatar()
                }, void 0, false, {
                    fileName: "src/pages/settings/UserProfileCard.tsx",
                    lineNumber: 115,
                    columnNumber: 11
                }, void 0),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                            level: 4,
                            style: {
                                margin: 0,
                                background: 'linear-gradient(135deg, #1890ff, #722ed1)',
                                WebkitBackgroundClip: 'text',
                                WebkitTextFillColor: 'transparent',
                                fontWeight: 600
                            },
                            children: "个人资料"
                        }, void 0, false, {
                            fileName: "src/pages/settings/UserProfileCard.tsx",
                            lineNumber: 127,
                            columnNumber: 13
                        }, void 0),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                            type: "secondary",
                            style: {
                                fontSize: '12px'
                            },
                            children: "管理您的个人信息和联系方式"
                        }, void 0, false, {
                            fileName: "src/pages/settings/UserProfileCard.tsx",
                            lineNumber: 139,
                            columnNumber: 13
                        }, void 0)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/settings/UserProfileCard.tsx",
                    lineNumber: 126,
                    columnNumber: 11
                }, void 0)
            ]
        }, void 0, true, {
            fileName: "src/pages/settings/UserProfileCard.tsx",
            lineNumber: 114,
            columnNumber: 9
        }, void 0),
        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
            form: form,
            layout: "vertical",
            onFinish: handleSaveProfile,
            autoComplete: "off",
            style: {
                marginTop: 16
            },
            children: [
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                    label: "用户名",
                    name: "name",
                    rules: [
                        {
                            required: true,
                            message: '请输入用户名'
                        },
                        {
                            max: 100,
                            message: '用户名不能超过100个字符'
                        },
                        {
                            min: 2,
                            message: '用户名至少需要2个字符'
                        }
                    ],
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                        prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {
                            style: {
                                color: '#1890ff'
                            }
                        }, void 0, false, {
                            fileName: "src/pages/settings/UserProfileCard.tsx",
                            lineNumber: 163,
                            columnNumber: 21
                        }, void 0),
                        placeholder: "请输入用户名",
                        size: "large"
                    }, void 0, false, {
                        fileName: "src/pages/settings/UserProfileCard.tsx",
                        lineNumber: 162,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/settings/UserProfileCard.tsx",
                    lineNumber: 153,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                    label: "邮箱地址",
                    name: "email",
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                        prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.MailOutlined, {
                            style: {
                                color: '#1890ff'
                            }
                        }, void 0, false, {
                            fileName: "src/pages/settings/UserProfileCard.tsx",
                            lineNumber: 171,
                            columnNumber: 21
                        }, void 0),
                        disabled: true,
                        placeholder: "邮箱地址不可修改",
                        size: "large",
                        style: {
                            backgroundColor: '#f5f5f5'
                        }
                    }, void 0, false, {
                        fileName: "src/pages/settings/UserProfileCard.tsx",
                        lineNumber: 170,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/settings/UserProfileCard.tsx",
                    lineNumber: 169,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                    label: "联系电话",
                    name: "telephone",
                    rules: [
                        {
                            max: 20,
                            message: '联系电话不能超过20个字符'
                        },
                        {
                            pattern: /^[0-9+\-\s()]*$/,
                            message: '请输入有效的电话号码'
                        }
                    ],
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                        prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.PhoneOutlined, {
                            style: {
                                color: '#1890ff'
                            }
                        }, void 0, false, {
                            fileName: "src/pages/settings/UserProfileCard.tsx",
                            lineNumber: 191,
                            columnNumber: 21
                        }, void 0),
                        placeholder: "请输入联系电话（可选）",
                        size: "large"
                    }, void 0, false, {
                        fileName: "src/pages/settings/UserProfileCard.tsx",
                        lineNumber: 190,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/settings/UserProfileCard.tsx",
                    lineNumber: 179,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Divider, {
                    style: {
                        margin: '24px 0'
                    }
                }, void 0, false, {
                    fileName: "src/pages/settings/UserProfileCard.tsx",
                    lineNumber: 197,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                    style: {
                        marginBottom: 0,
                        textAlign: 'right'
                    },
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                        type: "primary",
                        htmlType: "submit",
                        loading: loading,
                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SaveOutlined, {}, void 0, false, {
                            fileName: "src/pages/settings/UserProfileCard.tsx",
                            lineNumber: 204,
                            columnNumber: 19
                        }, void 0),
                        size: "large",
                        style: {
                            borderRadius: 8,
                            background: 'linear-gradient(135deg, #1890ff, #722ed1)',
                            border: 'none',
                            boxShadow: '0 2px 8px rgba(24, 144, 255, 0.3)',
                            minWidth: 120
                        },
                        children: "保存设置"
                    }, void 0, false, {
                        fileName: "src/pages/settings/UserProfileCard.tsx",
                        lineNumber: 200,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/settings/UserProfileCard.tsx",
                    lineNumber: 199,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "src/pages/settings/UserProfileCard.tsx",
            lineNumber: 146,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "src/pages/settings/UserProfileCard.tsx",
        lineNumber: 104,
        columnNumber: 5
    }, this);
};
_s(UserProfileCard, "KrK+UnzOqFoxC2B5IHxvtrTI5JE=", false, function() {
    return [
        _antd.Form.useForm,
        _max.useModel
    ];
});
_c = UserProfileCard;
var _default = UserProfileCard;
var _c;
$RefreshReg$(_c, "UserProfileCard");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/pages/settings/index.tsx": function (module, exports, __mako_require__){
"use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _max = __mako_require__("src/.umi/exports.ts");
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _react = /*#__PURE__*/ _interop_require_default._(__mako_require__("node_modules/react/index.js"));
var _FloatButton = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/components/FloatButton/index.tsx"));
var _TeamManagementCard = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/settings/TeamManagementCard.tsx"));
var _UserProfileCard = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/settings/UserProfileCard.tsx"));
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { Title, Text } = _antd.Typography;
/**
 * 设置页面组件
 *
 * 这是用户的设置主页面，提供用户个人设置、团队管理等功能。
 * 是用户进行各种配置和管理操作的主要入口页面。
 *
 * 页面功能：
 * 1. 用户个人信息设置
 * 2. 团队创建和管理
 * 3. 账户设置和偏好配置
 * 4. 订阅和计费管理
 *
 * 页面结构：
 * - 顶部：页面标题和描述
 * - 左侧：用户个人设置（响应式布局）
 * - 右侧：团队管理设置（响应式布局）
 * - 浮动：全局操作按钮
 *
 * 权限控制：
 * - 需要用户登录才能访问
 * - 由应用级路由守卫处理登录检查
 * - 支持登录状态变化的实时响应
 *
 * 响应式设计：
 * - 移动端：垂直堆叠布局
 * - 桌面端：左右分栏布局
 * - 自适应不同屏幕尺寸
 */ const SettingsPage = ()=>{
    _s();
    /**
   * 全局状态管理
   *
   * 从UmiJS全局状态中获取用户信息和加载状态：
   * - initialState: 包含用户和团队信息的全局状态
   * - loading: 全局状态的加载状态
   */ const { initialState, loading } = (0, _max.useModel)('@@initialState');
    /**
   * 返回上一页
   */ const handleGoBack = ()=>{
        _max.history.goBack();
    };
    /**
   * 加载状态处理
   *
   * 当全局状态正在初始化时，显示加载界面。
   * 这确保了用户在状态加载完成前看到友好的加载提示。
   */ if (loading) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
        style: {
            minHeight: '100vh',
            background: '#f5f8ff',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center'
        },
        children: [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
                size: "large"
            }, void 0, false, {
                fileName: "src/pages/settings/index.tsx",
                lineNumber: 73,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                style: {
                    marginLeft: 16
                },
                children: "正在加载设置信息..."
            }, void 0, false, {
                fileName: "src/pages/settings/index.tsx",
                lineNumber: 74,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "src/pages/settings/index.tsx",
        lineNumber: 64,
        columnNumber: 7
    }, this);
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_jsxdevruntime.Fragment, {
        children: [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                style: {
                    minHeight: '100vh',
                    background: 'linear-gradient(135deg, #f5f8ff 0%, #e8f4fd 100%)',
                    padding: '24px'
                },
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                        style: {
                            marginBottom: 24
                        },
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                style: {
                                    marginBottom: 16
                                },
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                    type: "text",
                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ArrowLeftOutlined, {}, void 0, false, {
                                        fileName: "src/pages/settings/index.tsx",
                                        lineNumber: 95,
                                        columnNumber: 21
                                    }, void 0),
                                    onClick: handleGoBack,
                                    style: {
                                        fontSize: '16px',
                                        color: '#1890ff',
                                        padding: '4px 8px'
                                    },
                                    children: "返回"
                                }, void 0, false, {
                                    fileName: "src/pages/settings/index.tsx",
                                    lineNumber: 93,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/settings/index.tsx",
                                lineNumber: 92,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                style: {
                                    textAlign: 'center'
                                },
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                        level: 2,
                                        style: {
                                            margin: 0,
                                            color: '#1890ff'
                                        },
                                        children: "设置中心"
                                    }, void 0, false, {
                                        fileName: "src/pages/settings/index.tsx",
                                        lineNumber: 109,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        type: "secondary",
                                        style: {
                                            fontSize: '16px'
                                        },
                                        children: "管理您的个人信息、团队设置和账户偏好"
                                    }, void 0, false, {
                                        fileName: "src/pages/settings/index.tsx",
                                        lineNumber: 112,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/settings/index.tsx",
                                lineNumber: 108,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/settings/index.tsx",
                        lineNumber: 90,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                        style: {
                            maxWidth: 1400,
                            margin: '0 auto',
                            borderRadius: 16,
                            boxShadow: '0 8px 32px rgba(0,0,0,0.1)',
                            border: 'none',
                            background: 'rgba(255, 255, 255, 0.95)',
                            backdropFilter: 'blur(10px)'
                        },
                        bodyStyle: {
                            padding: '32px'
                        },
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                            gutter: [
                                24,
                                24
                            ],
                            style: {
                                margin: 0
                            },
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                    xs: 24,
                                    sm: 24,
                                    md: 24,
                                    lg: 12,
                                    xl: 12,
                                    xxl: 12,
                                    style: {
                                        marginBottom: 8
                                    },
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_UserProfileCard.default, {}, void 0, false, {
                                        fileName: "src/pages/settings/index.tsx",
                                        lineNumber: 159,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/settings/index.tsx",
                                    lineNumber: 150,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                    xs: 24,
                                    sm: 24,
                                    md: 24,
                                    lg: 12,
                                    xl: 12,
                                    xxl: 12,
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_TeamManagementCard.default, {}, void 0, false, {
                                        fileName: "src/pages/settings/index.tsx",
                                        lineNumber: 172,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/settings/index.tsx",
                                    lineNumber: 171,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/settings/index.tsx",
                            lineNumber: 141,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/settings/index.tsx",
                        lineNumber: 118,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "src/pages/settings/index.tsx",
                lineNumber: 82,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_FloatButton.default, {}, void 0, false, {
                fileName: "src/pages/settings/index.tsx",
                lineNumber: 179,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true);
};
_s(SettingsPage, "J38jDe63PMq+vZFAyaRULBMLxho=", false, function() {
    return [
        _max.useModel
    ];
});
_c = SettingsPage;
var _default = SettingsPage;
var _c;
$RefreshReg$(_c, "SettingsPage");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
 }]);
//# sourceMappingURL=p__settings__index-async.js.map